name: Deploy Worker
on:
  workflow_dispatch:
  repository_dispatch:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '21'
      
      - name: Install dependencies
        run: npm install
      
      - name: Install wrangler
        run: npm install -g wrangler
      
      - name: Check and Create KV Namespace
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CF_ACCOUNT_ID }}
        run: |
          KV_NAMESPACE="SUBLINK_KV"
          echo "Checking for KV namespace: $KV_NAMESPACE"
          if ! LIST_OUTPUT=$(wrangler kv namespace list 2>&1); then
            echo "Error getting KV namespace list: $LIST_OUTPUT"
            exit 1
          fi
          
          LIST_OUTPUT=$(echo "$LIST_OUTPUT" | grep -v "Cloudflare collects" | grep -v "telemetry")
          echo "Cleaned KV namespace list output: $LIST_OUTPUT"
          
          if ! echo "$LIST_OUTPUT" | jq empty; then
            echo "Invalid JSON output from wrangler"
            exit 1
          fi
          
          KV_ID=$(echo "$LIST_OUTPUT" | jq -r '.[] | select(.title == "sublink-worker-'$KV_NAMESPACE'") | .id')
          
          if [ -z "$KV_ID" ]; then
              echo "KV namespace $KV_NAMESPACE does not exist. Creating..."
              CREATE_OUTPUT=$(wrangler kv namespace create "sublink-worker-$KV_NAMESPACE" 2>&1)
              echo "Create KV namespace output: $CREATE_OUTPUT"
              KV_ID=$(echo "$CREATE_OUTPUT" | grep -o '[0-9a-f]\{32\}')
              
              if [ -z "$KV_ID" ]; then
                  echo "Failed to extract KV ID. Full output: $CREATE_OUTPUT"
                  exit 1
              fi
              
              echo "KV namespace $KV_NAMESPACE created successfully with ID: $KV_ID"
          else
              echo "KV namespace $KV_NAMESPACE already exists with ID: $KV_ID"
          fi
          echo "KV_ID=$KV_ID" >> $GITHUB_ENV
          
      - name: Update wrangler.toml
        run: |
          # Read the entire content of wrangler.toml
          WRANGLER_CONTENT=$(cat wrangler.toml)
          
          # Update the KV namespace ID
          UPDATED_CONTENT=$(echo "$WRANGLER_CONTENT" | sed 's/id = "[^"]*"/id = "'$KV_ID'"/')
          
          # Write the updated content back to wrangler.toml
          echo "$UPDATED_CONTENT" > wrangler.toml
          
          echo "Updated wrangler.toml content:"
          cat wrangler.toml
          
      - name: Deploy to Cloudflare Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          command: deploy
