{"name": "sublink-plus", "version": "0.0.1", "private": true, "scripts": {"deploy": "npm run setup-kv && wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "setup-kv": "node scripts/setup-kv.js"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.0", "vitest": "^3.0.0 <3.1.0", "wrangler": "^3.60.3"}, "dependencies": {"js-yaml": "^4.1.0", "sublink-plus": "file:"}}